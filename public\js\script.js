// PQDI Item Search - Client-side JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('itemSearchForm');
    const resultsSection = document.getElementById('results');
    const resultsContent = document.getElementById('resultsContent');
    const searchBtn = document.querySelector('.search-btn');

    // Form submission handler
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Show loading state
        showLoading();
        
        // Collect form data
        const formData = new FormData(form);
        const searchParams = {};
        
        // Convert FormData to regular object
        for (let [key, value] of formData.entries()) {
            if (value.trim() !== '') {
                searchParams[key] = value;
            }
        }
        
        try {
            // Send search request to our server
            const response = await fetch('/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(searchParams)
            });
            
            const result = await response.json();
            
            // Display results
            displayResults(result);
            
        } catch (error) {
            console.error('Search error:', error);
            displayError('An error occurred while searching. Please try again.');
        } finally {
            hideLoading();
        }
    });

    function showLoading() {
        searchBtn.innerHTML = '<span class="loading"></span> Searching...';
        searchBtn.disabled = true;
    }

    function hideLoading() {
        searchBtn.innerHTML = 'Search Items';
        searchBtn.disabled = false;
    }

    function displayResults(result) {
        resultsSection.style.display = 'block';
        
        if (result.success) {
            resultsContent.innerHTML = `
                <div class="search-info">
                    <h4>Search Parameters:</h4>
                    <pre>${JSON.stringify(result.searchParams, null, 2)}</pre>
                    <p class="note">${result.message}</p>
                </div>
            `;
        } else {
            displayError(result.message || 'Search failed');
        }
        
        // Scroll to results
        resultsSection.scrollIntoView({ behavior: 'smooth' });
    }

    function displayError(message) {
        resultsSection.style.display = 'block';
        resultsContent.innerHTML = `
            <div class="error-message">
                <h4>Error</h4>
                <p>${message}</p>
            </div>
        `;
    }

    // Form validation and enhancement
    const itemNameInput = document.getElementById('itemName');
    const effectNameInput = document.getElementById('effectName');
    
    // Add input validation
    function validateForm() {
        const itemName = itemNameInput.value.trim();
        const effectName = effectNameInput.value.trim();
        const hasStats = document.querySelector('select[name="stat1"]').value !== '';
        const hasResist = document.querySelector('select[name="resist"]').value !== '';
        const hasContainer = document.getElementById('container').value !== '';
        
        // At least one search criteria should be provided
        if (!itemName && !effectName && !hasStats && !hasResist && !hasContainer) {
            return false;
        }
        
        return true;
    }

    // Real-time form validation
    form.addEventListener('input', function() {
        const isValid = validateForm();
        searchBtn.disabled = !isValid;
        
        if (!isValid) {
            searchBtn.style.opacity = '0.6';
        } else {
            searchBtn.style.opacity = '1';
        }
    });

    // Enhanced select interactions
    const selects = document.querySelectorAll('select');
    selects.forEach(select => {
        select.addEventListener('change', function() {
            if (this.value) {
                this.style.borderColor = 'var(--accent-primary)';
            } else {
                this.style.borderColor = 'var(--border-color)';
            }
        });
    });

    // Enhanced input interactions
    const inputs = document.querySelectorAll('input[type="text"], input[type="number"]');
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            if (this.value.trim()) {
                this.style.borderColor = 'var(--accent-primary)';
            } else {
                this.style.borderColor = 'var(--border-color)';
            }
        });
    });

    // Checkbox enhancements
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const label = this.closest('label');
            if (this.checked) {
                label.style.color = 'var(--accent-primary)';
            } else {
                label.style.color = 'var(--text-primary)';
            }
        });
    });

    // Clear form functionality
    function clearForm() {
        form.reset();
        resultsSection.style.display = 'none';
        
        // Reset visual states
        selects.forEach(select => {
            select.style.borderColor = 'var(--border-color)';
        });
        
        inputs.forEach(input => {
            input.style.borderColor = 'var(--border-color)';
        });
        
        checkboxes.forEach(checkbox => {
            const label = checkbox.closest('label');
            label.style.color = 'var(--text-primary)';
        });
    }

    // Add clear button
    const clearBtn = document.createElement('button');
    clearBtn.type = 'button';
    clearBtn.className = 'clear-btn';
    clearBtn.textContent = 'Clear Form';
    clearBtn.style.cssText = `
        background: transparent;
        color: var(--text-secondary);
        border: 2px solid var(--border-color);
        padding: 10px 20px;
        border-radius: 8px;
        cursor: pointer;
        margin-left: 15px;
        transition: all 0.3s ease;
    `;
    
    clearBtn.addEventListener('mouseenter', function() {
        this.style.borderColor = 'var(--border-hover)';
        this.style.color = 'var(--text-primary)';
    });
    
    clearBtn.addEventListener('mouseleave', function() {
        this.style.borderColor = 'var(--border-color)';
        this.style.color = 'var(--text-secondary)';
    });
    
    clearBtn.addEventListener('click', clearForm);
    
    document.querySelector('.submit-section').appendChild(clearBtn);

    // Initialize form state
    searchBtn.disabled = !validateForm();
    if (!validateForm()) {
        searchBtn.style.opacity = '0.6';
    }
});

// Add some CSS for the results display
const style = document.createElement('style');
style.textContent = `
    .search-info {
        background: var(--bg-primary);
        padding: 20px;
        border-radius: 8px;
        border: 1px solid var(--border-color);
    }
    
    .search-info h4 {
        color: var(--accent-primary);
        margin-bottom: 15px;
    }
    
    .search-info pre {
        background: var(--bg-secondary);
        padding: 15px;
        border-radius: 6px;
        overflow-x: auto;
        font-size: 0.9rem;
        border: 1px solid var(--border-color);
    }
    
    .search-info .note {
        margin-top: 15px;
        color: var(--text-secondary);
        font-style: italic;
    }
    
    .error-message {
        background: rgba(244, 67, 54, 0.1);
        border: 1px solid var(--error-color);
        padding: 20px;
        border-radius: 8px;
        color: var(--error-color);
    }
    
    .error-message h4 {
        margin-bottom: 10px;
    }
`;
document.head.appendChild(style);
