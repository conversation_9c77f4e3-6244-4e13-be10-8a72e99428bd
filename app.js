const express = require('express');
const axios = require('axios');
const cheerio = require('cheerio');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// Serve static files from public directory
app.use(express.static(path.join(__dirname, 'public')));

// Parse JSON and URL-encoded data
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Route for the main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Function to map our form fields to PQDI field names
function mapFormDataToPQDI(formData) {
    const pqdiData = {
        // Set default values for required fields
        item_name: formData.itemName || '',
        effect_name: formData.effectName || '',
        class_select: 'Class',
        race_select: 'Race',
        slot_select: 'Slot',
        type_select: 'Type',
        stat1_select: 'Stat',
        sel1_select: '>',
        stat1_int: '0',
        stat2_select: 'Stat',
        sel2_select: '>',
        stat2_int: '0',
        resist_select: 'Resist',
        res_select: '>',
        res_int: '0',
        bag_select: 'Container',
        bagslots: '0',
        wr: '0'
    };

    // Override defaults with actual values if provided
    if (formData.itemName) pqdiData.item_name = formData.itemName;
    if (formData.effectName) pqdiData.effect_name = formData.effectName;

    // Class mapping (name to numeric value)
    const classMap = {
        'Warrior': '1', 'Cleric': '2', 'Paladin': '4', 'Ranger': '8',
        'Shadow Knight': '16', 'Druid': '32', 'Monk': '64', 'Bard': '128',
        'Rogue': '256', 'Shaman': '512', 'Necromancer': '1024', 'Wizard': '2048',
        'Magician': '4096', 'Enchanter': '8192', 'Beastlord': '16384'
    };
    if (formData.class && classMap[formData.class]) {
        pqdiData.class_select = classMap[formData.class];
    }

    // Race mapping
    const raceMap = {
        'Human': '1', 'Barbarian': '2', 'Erudite': '4', 'Wood Elf': '8',
        'High Elf': '16', 'Dark Elf': '32', 'Half Elf': '64', 'Dwarf': '128',
        'Troll': '256', 'Ogre': '512', 'Halfling': '1024', 'Gnome': '2048',
        'Iksar': '4096', 'Vah Shir': '8192'
    };
    if (formData.race && raceMap[formData.race]) {
        pqdiData.race_select = raceMap[formData.race];
    }

    // Slot mapping
    const slotMap = {
        'Charm': '1', 'Ear': '2', 'Head': '4', 'Face': '8', 'Ears': '18',
        'Neck': '32', 'Shoulders': '64', 'Arms': '128', 'Back': '256',
        'Wrists': '1536', 'Range': '2048', 'Hands': '4096', 'Primary': '8192',
        'Secondary': '16384', 'Fingers': '98304', 'Chest': '131072',
        'Legs': '262144', 'Feet': '524288', 'Waist': '1048576', 'Ammo': '2097152'
    };
    if (formData.slot && slotMap[formData.slot]) {
        pqdiData.slot_select = slotMap[formData.slot];
    }

    // Item Type mapping
    const typeMap = {
        '1HS': '0', '2HS': '1', 'Piercing': '2', '1HB': '3', '2HB': '4',
        'Archery': '5', 'Throwing range items': '7', 'Shield': '8', 'Armor': '10',
        'Gems': '11', 'Lockpicks': '12', 'Food': '14', 'Drink': '15', 'Light': '16',
        'Combinable': '17', 'Bandages': '18', 'Throwing': '19', 'Scroll': '20',
        'Potion': '21', 'Wind Instrument': '23', 'Stringed Instrument': '24',
        'Brass Instrument': '25', 'Percussion Instrument': '26', 'Arrow': '27',
        'Jewelry': '29', 'Skull': '30', 'Tome': '31', 'Note': '32', 'Key': '33',
        'Coin': '34', '2H Piercing': '35', 'Fishing Pole': '36', 'Fishing Bait': '37',
        'Alcohol': '38', 'Key (bis)': '39', 'Compass': '40', 'Poison': '42',
        'Martial': '45', 'Charm': '52', 'Augmentation': '54'
    };
    if (formData.itemType && typeMap[formData.itemType]) {
        pqdiData.type_select = typeMap[formData.itemType];
    }

    // Stats mapping
    const statMap = {
        'Hit Points': 'hp', 'Mana': 'mana', 'AC': 'ac', 'Attack': 'attack',
        'Agility': 'aagi', 'Charisma': 'acha', 'Dexterity': 'adex',
        'Intelligence': 'aint', 'Stamina': 'asta', 'Strength': 'astr',
        'Wisdom': 'awis', 'Damage': 'damage', 'Delay': 'delay'
    };

    if (formData.stat1 && statMap[formData.stat1]) {
        pqdiData.stat1_select = statMap[formData.stat1];
        pqdiData.sel1_select = formData.stat1Operator || '>';
        pqdiData.stat1_int = formData.stat1Value || '0';
    }

    if (formData.stat2 && statMap[formData.stat2]) {
        pqdiData.stat2_select = statMap[formData.stat2];
        pqdiData.sel2_select = formData.stat2Operator || '>';
        pqdiData.stat2_int = formData.stat2Value || '0';
    }

    // Resist mapping
    const resistMap = {
        'Resist Magic': 'mr', 'Resist Fire': 'fr', 'Resist Cold': 'cr',
        'Resist Poison': 'pr', 'Resist Disease': 'dr'
    };
    if (formData.resist && resistMap[formData.resist]) {
        pqdiData.resist_select = resistMap[formData.resist];
        pqdiData.res_select = formData.resistOperator || '>';
        pqdiData.res_int = formData.resistValue || '0';
    }

    // Checkboxes
    if (formData.proc) pqdiData.has_proc = 'y';
    if (formData.click) pqdiData.has_click = 'y';
    if (formData.focus) pqdiData.has_focus = 'y';
    if (formData.worn) pqdiData.has_worn = 'y';
    if (formData.legacyItems) pqdiData.legacy = 'y';
    if (formData.tableView) pqdiData.table_view = 'y';

    // Container mapping
    const containerMap = {
        'Just a Bag': '1', 'Quiver': '2', 'Pouch': '3', 'Backpack': '5',
        'Tupperware': '6', 'Box': '7', 'Bandolier': '8', 'Alchemy': '9',
        'Tinkering': '10', 'Research': '11', 'Poison making': '12',
        'Special quests': '13', 'Baking: Mixing': '14', 'Baking: Cooking': '15',
        'Tailoring: Sewing Kit': '16', 'Fletching': '18', 'Brewing': '19',
        'Jewelry': '20', 'Wizard Research': '24', 'Mage Research': '25',
        'Necro Research': '26', 'Enchanter Research': '27', 'Plat Storage': '28',
        'Practice Research': '29', 'Pottery': '30', 'Tailoring: Vale': '41',
        'Tailoring: Erudite': '42', 'Tailoring: Fier\'Dal': '43', 'Fishing': '46',
        'Bazaar': '51'
    };
    if (formData.container && containerMap[formData.container]) {
        pqdiData.bag_select = containerMap[formData.container];
    }

    if (formData.bagSlots) pqdiData.bagslots = formData.bagSlots;
    if (formData.bagWeightReduction) pqdiData.wr = formData.bagWeightReduction;

    return pqdiData;
}

// Route to handle search requests
app.post('/search', async (req, res) => {
    try {
        console.log('Original search parameters:', req.body);

        // Map our form data to PQDI format
        const pqdiData = mapFormDataToPQDI(req.body);
        console.log('Mapped PQDI parameters:', pqdiData);

        // First, get the CSRF token from the PQDI items page
        const itemsPageResponse = await axios.get('https://www.pqdi.cc/items');
        const $ = cheerio.load(itemsPageResponse.data);
        const csrfToken = $('input[name="csrf_token"]').val();

        if (!csrfToken) {
            throw new Error('Could not retrieve CSRF token');
        }

        // Add CSRF token to our data
        pqdiData.csrf_token = csrfToken;

        // Make the search request to PQDI
        const searchResponse = await axios.post('https://www.pqdi.cc/items', pqdiData, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Referer': 'https://www.pqdi.cc/items'
            },
            transformRequest: [(data) => {
                return Object.keys(data)
                    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`)
                    .join('&');
            }]
        });

        // Parse the response HTML to extract search results
        console.log('PQDI Response length:', searchResponse.data.length);
        console.log('PQDI Response preview:', searchResponse.data.substring(0, 500));

        // Save response to file for debugging
        const fs = require('fs');
        fs.writeFileSync('debug_response.html', searchResponse.data);
        console.log('Response saved to debug_response.html');

        const results = parseSearchResults(searchResponse.data);

        res.json({
            success: true,
            message: `Found ${results.items.length} items`,
            searchParams: req.body,
            pqdiParams: pqdiData,
            results: results
        });

    } catch (error) {
        console.error('Search error:', error.message);
        res.status(500).json({
            success: false,
            message: 'Search failed: ' + error.message,
            searchParams: req.body
        });
    }
});

// Function to parse search results from PQDI HTML response
function parseSearchResults(html) {
    const $ = cheerio.load(html);
    const items = [];
    let totalResults = 0;
    let currentPage = 1;
    let totalPages = 1;

    console.log('Parsing HTML, looking for item links...');

    // Debug: log all links found
    const allLinks = $('a[href*="/item/"]');
    console.log('Found', allLinks.length, 'item links');

    // Check if there are results
    const resultText = $('body').text();
    if (resultText.includes('No items found') || resultText.includes('no items found')) {
        console.log('No items found message detected');
        return {
            items: [],
            totalResults: 0,
            currentPage: 1,
            totalPages: 1,
            hasResults: false
        };
    }

    // Parse pagination info if available
    const paginationText = $('.pagination').text();
    if (paginationText) {
        const pageMatch = paginationText.match(/Page (\d+) of (\d+)/);
        if (pageMatch) {
            currentPage = parseInt(pageMatch[1]);
            totalPages = parseInt(pageMatch[2]);
        }
    }

    // Parse item results - look for any element containing item links
    $('a[href*="/item/"]').each((index, element) => {
        const $link = $(element);
        const $parent = $link.closest('tr, .card, .item, div');

        const item = {};

        // Extract item name and link
        item.name = $link.text().trim();
        item.link = 'https://www.pqdi.cc' + $link.attr('href');
        item.id = $link.attr('href').match(/\/item\/(\d+)/)?.[1];

        console.log('Found item:', item.name, 'Link:', item.link);

        // Extract item stats from text content
        const itemText = $parent.text();

        // Try to extract AC
        const acMatch = itemText.match(/AC:\s*(\d+)/i);
        if (acMatch) item.ac = parseInt(acMatch[1]);

        // Try to extract HP
        const hpMatch = itemText.match(/HP:\s*([+-]?\d+)/i);
        if (hpMatch) item.hp = parseInt(hpMatch[1]);

        // Try to extract Mana
        const manaMatch = itemText.match(/MANA:\s*([+-]?\d+)/i);
        if (manaMatch) item.mana = parseInt(manaMatch[1]);

        // Try to extract stats (STR, DEX, etc.)
        const statMatches = itemText.match(/(STR|DEX|AGI|STA|INT|WIS|CHA):\s*([+-]?\d+)/gi);
        if (statMatches) {
            statMatches.forEach(match => {
                const [stat, value] = match.split(':');
                item[stat.toLowerCase().trim()] = parseInt(value.trim());
            });
        }

        // Try to extract damage/delay for weapons
        const damageMatch = itemText.match(/(\d+)\/(\d+)/);
        if (damageMatch) {
            item.damage = parseInt(damageMatch[1]);
            item.delay = parseInt(damageMatch[2]);
        }

        // Extract item type/slot info
        const slotMatch = itemText.match(/(PRIMARY|SECONDARY|CHEST|LEGS|ARMS|HEAD|FACE|NECK|SHOULDERS|BACK|WAIST|WRISTS|HANDS|FINGERS|FEET|EARS|RANGE|AMMO)/i);
        if (slotMatch) item.slot = slotMatch[1];

        // Extract class restrictions
        const classMatch = itemText.match(/(WAR|CLR|PAL|RNG|SHD|DRU|MNK|BRD|ROG|SHM|NEC|WIZ|MAG|ENC|BST)/g);
        if (classMatch) item.classes = classMatch;

        // Extract race restrictions
        const raceMatch = itemText.match(/(HUM|BAR|ERU|ELF|HIE|DEF|HEF|DWF|TRL|OGR|HFL|GNM|IKS|VAH)/g);
        if (raceMatch) item.races = raceMatch;

        // Only add if we found a name
        if (item.name) {
            items.push(item);
        }
    });

    // Try to get total results count
    const resultsCountText = $('.container').text();
    const countMatch = resultsCountText.match(/(\d+)\s+items?\s+found/i);
    if (countMatch) {
        totalResults = parseInt(countMatch[1]);
    } else {
        totalResults = items.length;
    }

    return {
        items: items,
        totalResults: totalResults,
        currentPage: currentPage,
        totalPages: totalPages,
        hasResults: items.length > 0
    };
}

app.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
});