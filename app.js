const express = require('express');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// Serve static files from public directory
app.use(express.static(path.join(__dirname, 'public')));

// Parse JSON and URL-encoded data
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Route for the main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Route to handle search requests (for future API integration)
app.post('/search', (req, res) => {
    // For now, just return the search parameters
    // In the future, this could make requests to PQDI API
    console.log('Search parameters:', req.body);
    res.json({
        success: true,
        message: 'Search functionality will be implemented here',
        searchParams: req.body
    });
});

app.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
});