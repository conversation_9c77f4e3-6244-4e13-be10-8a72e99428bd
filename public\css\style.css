/* Modern Dark Theme for PQDI Item Search */
:root {
    --bg-primary: #0f0f0f;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #2a2a2a;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #808080;
    --accent-primary: #4a9eff;
    --accent-secondary: #6b73ff;
    --border-color: #333333;
    --border-hover: #555555;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.4);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px 0;
    background: var(--bg-secondary);
    border-radius: 12px;
    box-shadow: var(--shadow);
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    background: linear-gradient(45deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

header p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.search-form {
    background: var(--bg-secondary);
    padding: 30px;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    margin-bottom: 30px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

label {
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-primary);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

input, select {
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
}

input:focus, select:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(74, 158, 255, 0.1);
}

input:hover, select:hover {
    border-color: var(--border-hover);
}

input::placeholder {
    color: var(--text-muted);
}

select option {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.stats-section, .resist-section, .container-section {
    margin: 30px 0;
    padding: 25px;
    background: var(--bg-tertiary);
    border-radius: 10px;
    border: 1px solid var(--border-color);
}

.stats-section h3, .resist-section h3, .container-section h3 {
    margin-bottom: 20px;
    color: var(--accent-primary);
    font-size: 1.3rem;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 10px;
}

.stat-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
    align-items: end;
}

.checkbox-section {
    margin: 25px 0;
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    font-weight: normal;
    text-transform: none;
    letter-spacing: normal;
    cursor: pointer;
    transition: color 0.3s ease;
}

.checkbox-group label:hover {
    color: var(--accent-primary);
}

.checkbox-group input[type="checkbox"] {
    margin-right: 8px;
    width: 18px;
    height: 18px;
    accent-color: var(--accent-primary);
}

.submit-section {
    text-align: center;
    margin-top: 40px;
}

.search-btn {
    background: linear-gradient(45deg, var(--accent-primary), var(--accent-secondary));
    color: white;
    border: none;
    padding: 15px 40px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: var(--shadow);
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.search-btn:active {
    transform: translateY(0);
}

.results-section {
    background: var(--bg-secondary);
    padding: 30px;
    border-radius: 12px;
    box-shadow: var(--shadow);
    margin-top: 30px;
}

.results-section h3 {
    color: var(--accent-primary);
    margin-bottom: 20px;
    font-size: 1.5rem;
}

#resultsContent {
    background: var(--bg-tertiary);
    padding: 20px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    min-height: 100px;
}

/* Search Results Styling */
.search-summary {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--border-color);
}

.search-summary h4 {
    color: var(--accent-primary);
    margin-bottom: 5px;
}

.search-summary p {
    color: var(--text-secondary);
    margin: 0;
}

.items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.item-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.item-card:hover {
    border-color: var(--accent-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.item-header {
    margin-bottom: 15px;
}

.item-header h5 {
    margin: 0;
    font-size: 1.2rem;
}

.item-header a {
    color: var(--accent-primary);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.item-header a:hover {
    color: var(--accent-secondary);
    text-decoration: underline;
}

.item-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.stat {
    background: var(--bg-secondary);
    color: var(--text-primary);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
    border: 1px solid var(--border-color);
}

.stat:hover {
    background: var(--bg-tertiary);
}

.item-slot, .item-classes, .item-races {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 8px;
    padding: 5px 0;
}

.item-slot {
    color: var(--accent-primary);
    font-weight: 500;
}

.item-classes, .item-races {
    font-family: monospace;
    background: var(--bg-secondary);
    padding: 8px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

.no-results {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
    font-size: 1.1rem;
    background: var(--bg-primary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .search-form {
        padding: 20px;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .checkbox-group {
        flex-direction: column;
        gap: 10px;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--accent-primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-primary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--border-hover);
}
