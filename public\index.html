<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PQDI Item Search - Modern Interface</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>PQDI Item Search</h1>
            <p>Modern interface for EverQuest item searching</p>
        </header>

        <main>
            <form id="itemSearchForm" class="search-form">
                <div class="form-grid">
                    <!-- Item Name -->
                    <div class="form-group full-width">
                        <label for="itemName">Item Name</label>
                        <input type="text" id="itemName" name="itemName" placeholder="Enter item name...">
                    </div>

                    <!-- Class Selection -->
                    <div class="form-group">
                        <label for="class">Class</label>
                        <select id="class" name="class">
                            <option value="">Any Class</option>
                            <option value="Warrior">Warrior</option>
                            <option value="Cleric">Cleric</option>
                            <option value="Paladin">Paladin</option>
                            <option value="Ranger">Ranger</option>
                            <option value="Shadow Knight">Shadow Knight</option>
                            <option value="Druid">Druid</option>
                            <option value="Monk">Monk</option>
                            <option value="Bard">Bard</option>
                            <option value="Rogue">Rogue</option>
                            <option value="Shaman">Shaman</option>
                            <option value="Necromancer">Necromancer</option>
                            <option value="Wizard">Wizard</option>
                            <option value="Magician">Magician</option>
                            <option value="Enchanter">Enchanter</option>
                            <option value="Beastlord">Beastlord</option>
                        </select>
                    </div>

                    <!-- Race Selection -->
                    <div class="form-group">
                        <label for="race">Race</label>
                        <select id="race" name="race">
                            <option value="">Any Race</option>
                            <option value="Vah Shir">Vah Shir</option>
                            <option value="Iksar">Iksar</option>
                            <option value="Gnome">Gnome</option>
                            <option value="Halfling">Halfling</option>
                            <option value="Ogre">Ogre</option>
                            <option value="Troll">Troll</option>
                            <option value="Dwarf">Dwarf</option>
                            <option value="Half Elf">Half Elf</option>
                            <option value="Dark Elf">Dark Elf</option>
                            <option value="High Elf">High Elf</option>
                            <option value="Wood Elf">Wood Elf</option>
                            <option value="Erudite">Erudite</option>
                            <option value="Barbarian">Barbarian</option>
                            <option value="Human">Human</option>
                        </select>
                    </div>

                    <!-- Slot Selection -->
                    <div class="form-group">
                        <label for="slot">Slot</label>
                        <select id="slot" name="slot">
                            <option value="">Any Slot</option>
                            <option value="Ammo">Ammo</option>
                            <option value="Waist">Waist</option>
                            <option value="Feet">Feet</option>
                            <option value="Legs">Legs</option>
                            <option value="Chest">Chest</option>
                            <option value="Fingers">Fingers</option>
                            <option value="Secondary">Secondary</option>
                            <option value="Primary">Primary</option>
                            <option value="Hands">Hands</option>
                            <option value="Range">Range</option>
                            <option value="Wrists">Wrists</option>
                            <option value="Back">Back</option>
                            <option value="Arms">Arms</option>
                            <option value="Shoulders">Shoulders</option>
                            <option value="Neck">Neck</option>
                            <option value="Ears">Ears</option>
                            <option value="Face">Face</option>
                            <option value="Head">Head</option>
                            <option value="Ear">Ear</option>
                            <option value="Charm">Charm</option>
                        </select>
                    </div>

                    <!-- Item Type -->
                    <div class="form-group">
                        <label for="itemType">Item Type</label>
                        <select id="itemType" name="itemType">
                            <option value="">Any Type</option>
                            <option value="1HS">1HS</option>
                            <option value="2HS">2HS</option>
                            <option value="Piercing">Piercing</option>
                            <option value="1HB">1HB</option>
                            <option value="2HB">2HB</option>
                            <option value="Archery">Archery</option>
                            <option value="Throwing range items">Throwing range items</option>
                            <option value="Shield">Shield</option>
                            <option value="Armor">Armor</option>
                            <option value="Gems">Gems</option>
                            <option value="Lockpicks">Lockpicks</option>
                            <option value="Food">Food</option>
                            <option value="Drink">Drink</option>
                            <option value="Light">Light</option>
                            <option value="Combinable">Combinable</option>
                            <option value="Bandages">Bandages</option>
                            <option value="Throwing">Throwing</option>
                            <option value="Scroll">Scroll</option>
                            <option value="Potion">Potion</option>
                            <option value="Wind Instrument">Wind Instrument</option>
                            <option value="Stringed Instrument">Stringed Instrument</option>
                            <option value="Brass Instrument">Brass Instrument</option>
                            <option value="Percussion Instrument">Percussion Instrument</option>
                            <option value="Arrow">Arrow</option>
                            <option value="Jewelry">Jewelry</option>
                            <option value="Skull">Skull</option>
                            <option value="Tome">Tome</option>
                            <option value="Note">Note</option>
                            <option value="Key">Key</option>
                            <option value="Coin">Coin</option>
                            <option value="2H Piercing">2H Piercing</option>
                            <option value="Fishing Pole">Fishing Pole</option>
                            <option value="Fishing Bait">Fishing Bait</option>
                            <option value="Alcohol">Alcohol</option>
                            <option value="Key (bis)">Key (bis)</option>
                            <option value="Compass">Compass</option>
                            <option value="Poison">Poison</option>
                            <option value="Martial">Martial</option>
                            <option value="Charm">Charm</option>
                            <option value="Augmentation">Augmentation</option>
                        </select>
                    </div>
                </div>

                <!-- Stats Section -->
                <div class="stats-section">
                    <h3>Stats</h3>
                    <div class="stat-row">
                        <select name="stat1">
                            <option value="">Select Stat</option>
                            <option value="Hit Points">Hit Points</option>
                            <option value="Mana">Mana</option>
                            <option value="AC">AC</option>
                            <option value="Attack">Attack</option>
                            <option value="Agility">Agility</option>
                            <option value="Charisma">Charisma</option>
                            <option value="Dexterity">Dexterity</option>
                            <option value="Intelligence">Intelligence</option>
                            <option value="Stamina">Stamina</option>
                            <option value="Strength">Strength</option>
                            <option value="Wisdom">Wisdom</option>
                            <option value="Damage">Damage</option>
                            <option value="Delay">Delay</option>
                        </select>
                        <select name="stat1Operator">
                            <option value=">">&gt;</option>
                            <option value=">=">&gt;=</option>
                            <option value="=">=</option>
                            <option value="<=">&lt;=</option>
                            <option value="<">&lt;</option>
                        </select>
                        <input type="number" name="stat1Value" placeholder="Value">
                    </div>
                    <div class="stat-row">
                        <select name="stat2">
                            <option value="">Select Stat</option>
                            <option value="Hit Points">Hit Points</option>
                            <option value="Mana">Mana</option>
                            <option value="AC">AC</option>
                            <option value="Attack">Attack</option>
                            <option value="Agility">Agility</option>
                            <option value="Charisma">Charisma</option>
                            <option value="Dexterity">Dexterity</option>
                            <option value="Intelligence">Intelligence</option>
                            <option value="Stamina">Stamina</option>
                            <option value="Strength">Strength</option>
                            <option value="Wisdom">Wisdom</option>
                            <option value="Damage">Damage</option>
                            <option value="Delay">Delay</option>
                        </select>
                        <select name="stat2Operator">
                            <option value=">">&gt;</option>
                            <option value=">=">&gt;=</option>
                            <option value="=">=</option>
                            <option value="<=">&lt;=</option>
                            <option value="<">&lt;</option>
                        </select>
                        <input type="number" name="stat2Value" placeholder="Value">
                    </div>
                </div>

                <!-- Resist Section -->
                <div class="resist-section">
                    <h3>Resist</h3>
                    <div class="stat-row">
                        <select name="resist">
                            <option value="">Select Resist</option>
                            <option value="Resist Magic">Resist Magic</option>
                            <option value="Resist Fire">Resist Fire</option>
                            <option value="Resist Cold">Resist Cold</option>
                            <option value="Resist Poison">Resist Poison</option>
                            <option value="Resist Disease">Resist Disease</option>
                        </select>
                        <select name="resistOperator">
                            <option value=">">&gt;</option>
                            <option value=">=">&gt;=</option>
                            <option value="=">=</option>
                            <option value="<=">&lt;=</option>
                            <option value="<">&lt;</option>
                        </select>
                        <input type="number" name="resistValue" placeholder="Value">
                    </div>
                </div>

                <!-- Checkboxes -->
                <div class="checkbox-section">
                    <div class="checkbox-group">
                        <label><input type="checkbox" name="proc" value="1"> Proc?</label>
                        <label><input type="checkbox" name="click" value="1"> Click?</label>
                        <label><input type="checkbox" name="focus" value="1"> Focus?</label>
                        <label><input type="checkbox" name="worn" value="1"> Worn?</label>
                    </div>
                </div>

                <!-- Effect Name -->
                <div class="form-group full-width">
                    <label for="effectName">Effect Name</label>
                    <input type="text" id="effectName" name="effectName" placeholder="Enter effect name...">
                </div>

                <!-- Container Section -->
                <div class="container-section">
                    <h3>Container</h3>
                    <div class="form-group">
                        <label for="container">Container Type</label>
                        <select id="container" name="container">
                            <option value="">Any Container</option>
                            <option value="Just a Bag">Just a Bag</option>
                            <option value="Quiver">Quiver</option>
                            <option value="Pouch">Pouch</option>
                            <option value="Backpack">Backpack</option>
                            <option value="Tupperware">Tupperware</option>
                            <option value="Box">Box</option>
                            <option value="Bandolier">Bandolier</option>
                            <option value="Alchemy">Alchemy</option>
                            <option value="Tinkering">Tinkering</option>
                            <option value="Research">Research</option>
                            <option value="Poison making">Poison making</option>
                            <option value="Special quests">Special quests</option>
                            <option value="Baking: Mixing">Baking: Mixing</option>
                            <option value="Baking: Cooking">Baking: Cooking</option>
                            <option value="Tailoring: Sewing Kit">Tailoring: Sewing Kit</option>
                            <option value="Fletching">Fletching</option>
                            <option value="Brewing">Brewing</option>
                            <option value="Jewelry">Jewelry</option>
                            <option value="Wizard Research">Wizard Research</option>
                            <option value="Mage Research">Mage Research</option>
                            <option value="Necro Research">Necro Research</option>
                            <option value="Enchanter Research">Enchanter Research</option>
                            <option value="Plat Storage">Plat Storage</option>
                            <option value="Practice Research">Practice Research</option>
                            <option value="Pottery">Pottery</option>
                            <option value="Tailoring: Vale">Tailoring: Vale</option>
                            <option value="Tailoring: Erudite">Tailoring: Erudite</option>
                            <option value="Tailoring: Fier'Dal">Tailoring: Fier'Dal</option>
                            <option value="Fishing">Fishing</option>
                            <option value="Bazaar">Bazaar</option>
                        </select>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="bagSlots">Bag Slots >=</label>
                            <input type="number" id="bagSlots" name="bagSlots" min="0">
                        </div>
                        <div class="form-group">
                            <label for="bagWeightReduction">Bag Weight Reduction >=</label>
                            <input type="number" id="bagWeightReduction" name="bagWeightReduction" min="0">
                        </div>
                    </div>
                </div>

                <!-- Additional Options -->
                <div class="checkbox-section">
                    <div class="checkbox-group">
                        <label><input type="checkbox" name="legacyItems" value="1"> Legacy Items</label>
                        <label><input type="checkbox" name="tableView" value="1"> Table View</label>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="submit-section">
                    <button type="submit" class="search-btn">Search Items</button>
                </div>
            </form>

            <!-- Results Section -->
            <div id="results" class="results-section" style="display: none;">
                <h3>Search Results</h3>
                <div id="resultsContent"></div>
            </div>
        </main>
    </div>

    <script src="js/script.js"></script>
</body>
</html>
