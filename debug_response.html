<!DOCTYPE html>
<html lang="en" class="h-100" data-bs-theme="auto">
<head>
    
    
    
    
    
    
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-JRMK1JFQTY"></script>
    <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-JRMK1JFQTY');
    </script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"> 
    <script type="module" src="https://ajax.googleapis.com/ajax/libs/model-viewer/3.4.0/model-viewer.min.js"></script>


<script src="/static/color-modes.js"></script>
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
<script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    
    <link rel="stylesheet" href="https://cdn.datatables.net/2.0.3/css/dataTables.dataTables.css" />
  
<script src="https://cdn.datatables.net/2.0.3/js/dataTables.js"></script>
    
    
    <link rel="stylesheet" href="/static/style.css">
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
     
<title>Items :: PQDI</title>

    
</head>
<body class="d-flex flex-column h-100">
<nav class="navbar navbar-expand-lg bg-body-tertiary fixed-top border-bottom">
  <div class="container-fluid">
    <a class="navbar-brand" href="/"><svg class="bi" fill="currentColor" width="1.5em" height="1.5em"><use href="/static/bootstrap-icons.svg#house-fill"></use></svg></a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarSupportedContent">
      <ul class="navbar-nav me-auto mb-0 mb-lg-0">
        <li class="nav-item"><a href="/zones" class="nav-link">Zones</a></li>
                <li class="nav-item"><a href="/items" class="nav-link">Items</a></li>
                <li class="nav-item"><a href="/factions" class="nav-link">Factions</a></li>
                <li class="nav-item"><a href="/npcs" class="nav-link">Bestiary</a></li>
                <li class="nav-item"><a href="/spells" class="nav-link">Spells</a></li>
                <li class="nav-item"><a href="/recipes" class="nav-link">Recipes</a></li>
                        <li class="nav-item dropdown">
          <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
            Misc
          </a>
          <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="/zem">ZEM</a></li>
            <li><a class="dropdown-item" href="/exp">Exp per Level</a></li>
            <li><a class="dropdown-item" href="/characters">Class/Race/Faction</a></li>
            <li><a class="dropdown-item" href="/pets">Pets</a></li>
            <li><a class="dropdown-item" href="/skills">Skill Caps</a></li>
            <li><a class="dropdown-item" href="/lootfinder">Loot by Level</a></li>
            <li><a class="dropdown-item" href="/missing-spells">Missing Spells</a></li>
            <li><a class="dropdown-item" href="/logparse">Log Parser</a></li>
            <li><a class="dropdown-item" href="/fishing">Fishin'</a></li>
          </ul>
        </li>
        <li class="nav-item "><a href="/instances" class="nav-link text-danger-emphasis"><strong>Pro Raider Info</strong></a></li>
      </ul>

      
      <div class="dropdown px-3">
      <button class="btn border-0 py-0 dropdown-toggle align-items-center"
              id="bd-theme"
              type="button"
              aria-expanded="false"
              data-bs-toggle="dropdown"
              aria-label="Toggle theme (auto)">
        <svg class="bi my-1 theme-icon-active" fill="currentColor" width="1.5em" height="1.5em"><use href="/static/bootstrap-icons.svg#circle-half"></use></svg>
        <span class="visually-hidden" id="bd-theme-text">Toggle theme</span>
      </button>
      <ul class="dropdown-menu" aria-labelledby="bd-theme-text">
        <li>
          <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="light" aria-pressed="false">
            <svg class="bi me-2 opacity-50" width="1em" height="1em"><use href="/static/bootstrap-icons.svg#sun-fill"></use></svg>
            Light
            <svg class="bi ms-auto d-none" width="1em" height="1em"><use href="/static/bootstrap-icons.svg#check2"></use></svg>
          </button>
        </li>
        <li>
          <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark" aria-pressed="false">
            <svg class="bi me-2 opacity-50" width="1em" height="1em"><use href="/static/bootstrap-icons.svg#moon-stars-fill"></use></svg>
            Dark
            <svg class="bi ms-auto d-none" width="1em" height="1em"><use href="/static/bootstrap-icons.svg#check2"></use></svg>
          </button>
        </li>
        <li>
          <button type="button" class="dropdown-item d-flex align-items-center active" data-bs-theme-value="auto" aria-pressed="true">
            <svg class="bi me-2 opacity-50" width="1em" height="1em"><use href="/static/bootstrap-icons.svg#circle-half"></use></svg>
            Auto
            <svg class="bi ms-auto d-none" width="1em" height="1em"><use href="/static/bootstrap-icons.svg#check2"></use></svg>
          </button>
        </li>
      </ul>
      </div>
        
      

      <form class="d-flex input-group w-auto" role="search" id="searchForm" action="/" method="post" novalidate>
        <input id="csrf_token" name="csrf_token" type="hidden" value="IjA3MDFlMzBmY2ZlNmEwYTM1YTMwODJlOWYwZjdkYmJiZDg3NTk4ZmEi.aDyXJA.Uv4MTMDRn3C8l6fuFhnBBjn8mJ4">
        <input class="form-control" id="search" name="search" required type="text" value="">
        <input class="btn btn-sm btn-outline-secondary" id="submit" name="submit" type="submit" value="Search">
        
      </form>
    </div>
    
  </div>
</nav>
    
    <div style="height:1px; margin-top:2em;"></div>
    <main class="flex-shrink-0 pt-4">
    <div class="container py-4">
    
    

<h1 class="text-center">Item Search</h1>
<div class="row align-items-md-stretch">
    <details open="true">
        <summary class="none">
            <article class="bg-body-tertiary border">
                <nav>
                    <div class="max">Item Search Form</div>
                </nav>
            </article>
        </summary>

        <form class="p-3 mb-4 bg-body-tertiary border" action="" method="post" novalidate>
            <input id="csrf_token" name="csrf_token" type="hidden" value="IjA3MDFlMzBmY2ZlNmEwYTM1YTMwODJlOWYwZjdkYmJiZDg3NTk4ZmEi.aDyXJA.Uv4MTMDRn3C8l6fuFhnBBjn8mJ4">
            <div class="col-12">
                <input class="form-control" id="item_name" name="item_name" required type="text" value="Bone Bladed Claymore">
                <div class="form-text text-wrap"><label for="item_name">Item Name</label></div>
            </div>
            <div class="row py-3">
                <div class="col-12 col-md-3">
                    <select class="form-select" id="class_select" name="class_select"><option selected value="Class">Class</option><option value="1">Warrior</option><option value="2">Cleric</option><option value="4">Paladin</option><option value="8">Ranger</option><option value="16">Shadow Knight</option><option value="32">Druid</option><option value="64">Monk</option><option value="128">Bard</option><option value="256">Rogue</option><option value="512">Shaman</option><option value="1024">Necromancer</option><option value="2048">Wizard</option><option value="4096">Magician</option><option value="8192">Enchanter</option><option value="16384">Beastlord</option></select>
                </div>
                <div class="col-12 col-md-3">
                    <select class="form-select" id="race_select" name="race_select"><option selected value="Race">Race</option><option value="8192">Vah Shir</option><option value="4096">Iksar</option><option value="2048">Gnome</option><option value="1024">Halfling</option><option value="512">Ogre</option><option value="256">Troll</option><option value="128">Dwarf</option><option value="64">Half Elf</option><option value="32">Dark Elf</option><option value="16">High Elf</option><option value="8">Wood Elf</option><option value="4">Erudite</option><option value="2">Barbarian</option><option value="1">Human</option></select>
                </div>
                <div class="col-12 col-md-3">
                    <select class="form-select" id="slot_select" name="slot_select"><option selected value="Slot">Slot</option><option value="2097152">Ammo</option><option value="1048576">Waist</option><option value="524288">Feet</option><option value="262144">Legs</option><option value="131072">Chest</option><option value="98304">Fingers</option><option value="16384">Secondary</option><option value="8192">Primary</option><option value="4096">Hands</option><option value="2048">Range</option><option value="1536">Wrists</option><option value="256">Back</option><option value="128">Arms</option><option value="64">Shoulders</option><option value="32">Neck</option><option value="18">Ears</option><option value="8">Face</option><option value="4">Head</option><option value="2">Ear</option><option value="1">Charm</option></select>
                </div>
                <div class="col-12 col-md-3">
                    <select class="form-select" id="type_select" name="type_select"><option selected value="Type">Item Type</option><option value="0">1HS</option><option value="1">2HS</option><option value="2">Piercing</option><option value="3">1HB</option><option value="4">2HB</option><option value="5">Archery</option><option value="7">Throwing range items</option><option value="8">Shield</option><option value="10">Armor</option><option value="11">Gems</option><option value="12">Lockpicks</option><option value="14">Food</option><option value="15">Drink</option><option value="16">Light</option><option value="17">Combinable</option><option value="18">Bandages</option><option value="19">Throwing</option><option value="20">Scroll</option><option value="21">Potion</option><option value="23">Wind Instrument</option><option value="24">Stringed Instrument</option><option value="25">Brass Instrument</option><option value="26">Percussion Instrument</option><option value="27">Arrow</option><option value="29">Jewelry</option><option value="30">Skull</option><option value="31">Tome</option><option value="32">Note</option><option value="33">Key</option><option value="34">Coin</option><option value="35">2H Piercing</option><option value="36">Fishing Pole</option><option value="37">Fishing Bait</option><option value="38">Alcohol</option><option value="39">Key (bis)</option><option value="40">Compass</option><option value="42">Poison</option><option value="45">Martial</option><option value="52">Charm</option><option value="54">Augmentation</option></select>
                </div>
            </div>
            <div class="row">
                <div class="col-12 col-md-3">
                    <div class="d-flex">
                        <div class="col">
                            <select class="form-select" id="stat1_select" name="stat1_select" style="width: 14ch"><option selected value="Stat">Stat</option><option value="hp">Hit Points</option><option value="mana">Mana</option><option value="ac">AC</option><option value="attack">Attack</option><option value="aagi">Agility</option><option value="acha">Charisma</option><option value="adex">Dexterity</option><option value="aint">Intelligence</option><option value="asta">Stamina</option><option value="astr">Strength</option><option value="awis">Wisdom</option><option value="damage">Damage</option><option value="delay">Delay</option></select>
                        </div>
                        <div class="col">
                            <select class="form-select" id="sel1_select" name="sel1_select" style="width: 10ch"><option selected value="&gt;">&gt;</option><option value="&gt;=">&gt;=</option><option value="=">=</option><option value="&lt;=">&lt;=</option><option value="&lt;">&lt;</option></select>
                        </div>
                        <div class="col">
                            <input class="form-control" id="stat1_int" name="stat1_int" style="width: 10ch" type="number" value="0">
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-3">
                    <div class="d-flex">
                        <div class="col">
                            <select class="form-select" id="stat2_select" name="stat2_select" style="width: 14ch"><option selected value="Stat">Stat</option><option value="hp">Hit Points</option><option value="mana">Mana</option><option value="ac">AC</option><option value="attack">Attack</option><option value="aagi">Agility</option><option value="acha">Charisma</option><option value="adex">Dexterity</option><option value="aint">Intelligence</option><option value="asta">Stamina</option><option value="astr">Strength</option><option value="awis">Wisdom</option><option value="damage">Damage</option><option value="delay">Delay</option></select>
                        </div>
                        <div class="col">
                            <select class="form-select" id="sel2_select" name="sel2_select" style="width: 10ch"><option selected value="&gt;">&gt;</option><option value="&gt;=">&gt;=</option><option value="=">=</option><option value="&lt;=">&lt;=</option><option value="&lt;">&lt;</option></select>
                        </div>
                        <div class="col">
                            <input class="form-control" id="stat2_int" name="stat2_int" style="width: 10ch" type="number" value="0">
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-3">
                    <div class="d-flex">
                        <div class="col">
                            <select class="form-select" id="resist_select" name="resist_select" style="width: 14ch"><option selected value="Resist">Resist</option><option value="mr">Resist Magic</option><option value="fr">Resist Fire</option><option value="cr">Resist Cold</option><option value="pr">Resist Poison</option><option value="dr">Resist Disease</option></select>
                        </div>
                        <div class="col">
                            <select class="form-select" id="res_select" name="res_select" style="width: 10ch"><option selected value="&gt;">&gt;</option><option value="&gt;=">&gt;=</option><option value="=">=</option><option value="&lt;=">&lt;=</option><option value="&lt;">&lt;</option></select>
                        </div>
                        <div class="col">
                            <input class="form-control" id="res_int" name="res_int" style="width: 10ch" type="number" value="0">
                        </div>
                    </div>
                </div>

                <div class="col-12 col-md-3">
                    <div class="d-flex">
                    <div class="col">
                    <div class="form-check">
                    <input class="form-check-input" id="has_proc" name="has_proc" type="checkbox" value="y">
                    <label class="form-check-label"><label for="has_proc">Proc?</label></label>
                </div>
                    <div class="form-check">
                    <input class="form-check-input" id="has_click" name="has_click" type="checkbox" value="y">
                    <label class="form-check-label"><label for="has_click">Click?</label></label>
                </div>
                </div>
                <div class="col">
                <div class="form-check">
                    <input class="form-check-input" id="has_focus" name="has_focus" type="checkbox" value="y">
                    <label class="form-check-label"><label for="has_focus">Focus?</label></label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" id="has_worn" name="has_worn" type="checkbox" value="y">
                    <label class="form-check-label"><label for="has_worn">Worn?</label></label>
                </div>
                </div>
                    </div>
                </div>
            </div>
            <div class="form-text">Weapon Damage and Delay are under Stat dropdowns</div>
            <div class="col-12 py-3">
                <input class="form-control" id="effect_name" name="effect_name" required type="text" value="">
                <div class="form-text text-wrap"><label for="effect_name">Effect Name</label></div>
            </div>
            

            <div class="row">
                <div class="col-12 col-md-4">
                    <select class="form-select" id="bag_select" name="bag_select"><option selected value="Container">Container</option><option value="1">Just a Bag</option><option value="2">Quiver</option><option value="3">Pouch</option><option value="4">Pouch</option><option value="5">Backpack</option><option value="6">Tupperware</option><option value="7">Box</option><option value="8">Bandolier</option><option value="9">Alchemy</option><option value="10">Tinkering</option><option value="11">Research</option><option value="12">Poison making</option><option value="13">Special quests</option><option value="14">Baking: Mixing</option><option value="15">Baking: Cooking</option><option value="16">Tailoring: Sewing Kit</option><option value="18">Fletching</option><option value="19">Brewing</option><option value="20">Jewelry</option><option value="24">Wizard Research</option><option value="25">Mage Research</option><option value="26">Necro Research</option><option value="27">Enchanter Research</option><option value="28">Plat Storage</option><option value="29">Practice Research</option><option value="30">Pottery</option><option value="41">Tailoring: Vale</option><option value="42">Tailoring: Erudite</option><option value="43">Tailoring: Fier`Dal</option><option value="44">Fletching</option><option value="46">Fishing</option><option value="51">Bazaar</option></select>
                    <div class="form-text text-wrap"><label for="bag_select">Container Type (not required for slots or WR fields)</label></div>
                </div>
                <div class="col-12 col-md-2 field small">
                    <input class="form-control" id="bagslots" name="bagslots" type="number" value="0">
                    <div class="form-text text-wrap"><label for="bagslots">Bag Slots &gt;=</label></div>
                </div>
                <div class="col-12 col-md-2 field small">
                    <input class="form-control" id="wr" name="wr" type="number" value="0">
                    <div class="form-text text-wrap"><label for="wr">Bag Weight Reduction &gt;=</label></div>
                </div>
                <div class="col-12 col-md-2 form-check">
                    <input class="form-check-input" id="legacy" name="legacy" type="checkbox" value="y">
                    <label class="form-check-label"><label for="legacy">Legacy Items</label></label>
                </div>
                <div class="col-12 col-md-2 form-check">
                    <input class="form-check-input" id="table_view" name="table_view" type="checkbox" value="y">
                    <label class="form-check-label"><label for="table_view">Table View</label></label>
                </div>
            </div>
            <div class="row align-items-md-stretch">
    <div class="col-12 col-md-12">
        <div class="d-grid gap-2">
            <input class="btn btn-outline-secondary" id="submit" name="submit" type="submit" value="Search">
        </div>
    </div>
</div>

        </form>
    </details>
</div>


<script>
  $(document).ready(function () {
    // Initialize DataTable plugin and store the instance in the 'table' variable
    let table = $('#sortable-table-1').DataTable({
        paging: true,
        pageLength: 50,
        lengthChange: false
    });

    // Attach event listeners to toggle column visibility
    document.querySelectorAll('a.toggle-vis').forEach((el) => {
        el.addEventListener('click', function (e) {
            e.preventDefault();

            let columnIdx = e.target.getAttribute('data-column');
            let column = table.column(columnIdx);

            // Toggle the visibility
            column.visible(!column.visible());
        });
    });
});

</script>

    </div>
</main>

<script src="/static/popper.min.js"></script>
<script src="/static/tippy-bundle.umd.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        tippy('.tooltip-link', {
            content: 'Loading...',
            allowHTML: true,
            maxWidth: 'none',
            onShow(instance) {
                const url = instance.reference.dataset.url;
                fetch(`/get-item-tooltip/${encodeURIComponent(url)}`)
                    .then(response => response.text())
                    .then(content => {
                        instance.setContent(content);
                    })
                    .catch(error => {
                        console.error('Error fetching tooltip content:', error);
                    });
            },
        });
    });
</script>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        tippy('.spell-tooltip', {
            content: 'Loading...',
            allowHTML: true,
            maxWidth: 'none',
            onShow(instance) {
                const url = instance.reference.dataset.url;
                fetch(`/api/v1/spell/${encodeURIComponent(url)}`)
                    .then(response => response.json())
                    .then(data => {
                        const content = formatTooltip(data);
                        instance.setContent(content);
                    })
                    .catch(error => {
                        console.error('Error fetching tooltip content:', error);
                    });
            },
        });

        function formatTooltip(data) {
            let effectsList = '';

            if (data.effects && Array.isArray(data.effects)) {
                effectsList = '<ul>';
                data.effects.forEach(effect => {
                    effectsList += `<li>${effect}</li>`;
                });
                effectsList += '</ul>';
            }
            
            return `
                <div class="tooltip-content">
                    <h3>${data.name}</h3>
                    <p><strong>Effects:</strong> ${effectsList}</p>
                </div>
            `;
        }
    });
</script>

<footer class="footer mt-auto">
      <div class="container">
        <p class="text-muted text-center">Database version:    quarm_2025-04-12-16_11 (see diff by Icestorm on <a href="https://yaqds.cc/database/changes/" target="_blank">YAQDS</a>)</p>
      </div>
    </footer>
</body>


</html>
