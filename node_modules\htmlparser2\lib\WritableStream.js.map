{"version": 3, "file": "WritableStream.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/htmlparser2/e2939a6c7b05d5c4845b4a2e458a4fc0a65a321d/src/", "sources": ["WritableStream.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,yCAAuE;AACvE;;;GAGG;AACH,2CAAuC;AACvC,2DAAoD;AAEpD,2GAA2G;AAC3G,SAAS,QAAQ,CAAC,MAAuB,EAAE,QAAgB;IACvD,OAAO,QAAQ,KAAK,QAAQ,CAAC;AACjC,CAAC;AAED;;;;GAIG;AACH;IAAoC,kCAAQ;IAIxC,wBAAY,GAAqB,EAAE,OAAuB;QACtD,YAAA,MAAK,YAAC,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC,SAAC;QAHnB,cAAQ,GAAG,IAAI,mCAAa,EAAE,CAAC;QAI5C,KAAI,CAAC,OAAO,GAAG,IAAI,kBAAM,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;;IAC5C,CAAC;IAEQ,+BAAM,GAAf,UACI,KAAsB,EACtB,QAAgB,EAChB,QAAoB;QAEpB,IAAI,CAAC,OAAO,CAAC,KAAK,CACd,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CACjE,CAAC;QACF,QAAQ,EAAE,CAAC;IACf,CAAC;IAEQ,+BAAM,GAAf,UAAgB,QAAoB;QAChC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC;QACtC,QAAQ,EAAE,CAAC;IACf,CAAC;IACL,qBAAC;AAAD,CAAC,AAxBD,CAAoC,sBAAQ,GAwB3C;AAxBY,wCAAc"}